#!/usr/bin/env bash
# shellcheck disable=SC1091

other_args=(
    --spaces buy sell stoploss
    --hyperopt-loss OnlyProfitHyperOptLoss
    # --enable-protections
    --epochs 300
    # --disable-param-export  # 禁用JSON文件生成
)
# buy sell roi stoploss trailing protection trades
# WinRateLoss: 仅考虑胜率。
# OnlyProfitHyperOptLoss: 仅考虑利润金额。
# SharpeHyperOptLoss: 优化根据标准差计算的交易回报夏普比率。
# SharpeHyperOptLossDaily: 优化根据每日交易回报相对于标准差计算的夏普比率。
# ShortTradeDurHyperOptLoss: (默认的传统 Freqtrade 超优化损失函数) - 主要用于短期交易持续时间和避免损失。
# SortinoHyperOptLoss: 优化根据交易回报相对于下行标准差计算的索提诺比率。
# SortinoHyperOptLossDaily: 优化根据每日交易回报相对于下行标准差计算的索提诺比率。
# MaxDrawDownHyperOptLoss: 优化最大绝对回撤。
# MaxDrawDownRelativeHyperOptLoss: 优化最大绝对回撤，同时调整最大相对回撤。
# MaxDrawDownPerPairHyperOptLoss: 计算每对交易对的盈利/回撤比率，并返回最差结果作为目标，从而强制 Hyperopt 优化交易对列表中所有交易对的参数。这样，​​我们可以防止一个或多个结果良好的交易对夸大指标，而结果较差的交易对则不会被体现，因此也不会被优化。
# CalmarHyperOptLoss: 优化根据最大回撤相关的交易回报计算的 Calmar 比率。
# ProfitDrawDownHyperOptLoss: 通过最大利润和最小回撤目标进行优化。hyperoptlossDRAWDOWN_MULT文件中的变量可以根据回撤目的进行调整，使其更严格或更灵活。
# MultiMetricHyperOptLoss: 通过多项关键指标进行优化，以实现均衡的性能。主要关注点在于最大化利润并最小化回撤，同时还考虑其他指标，例如利润因子、预期比率和胜率。此外，系统会对交易次数较少的周期施加惩罚，以鼓励交易频率充足的策略。

# 显示帮助信息
show_help() {
    cat <<EOF
用法: $0 [选项]

选项:
    -h, --help              显示此帮助信息
    -c, --config FILE  配置文件 (覆盖 CONFIG_FILE)
    -s, --strategy NAME     策略名称 (覆盖 STRATEGY)
    --start-date DATE       开始日期 (覆盖 START_DATE)
    --end-date DATE         结束日期 (覆盖 END_DATE)
    --strategy-path PATH    策略路径 (覆盖 STRATEGY_PATH)
    --freqai-model MODEL 使用FreqAI模型

示例:
    $0 -s MyStrategy --start-date 20240101
    $0 -d /path/to/data -c config/test.json
    $0 --strategy-path custom/strategies -s TestStrategy

EOF
}

# 切换到脚本目录
cd "$(dirname "$0")" || exit

# 加载 .env 文件中的默认值
source .env

# 默认设置
FREQAI_MODEL=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
    -h | --help)
        show_help
        exit 0
        ;;
    -c | --config)
        CONFIG_FILE="$2"
        shift 2
        ;;
    -s | --strategy)
        STRATEGY="$2"
        shift 2
        ;;
    --start-date)
        START_DATE="$2"
        shift 2
        ;;
    --end-date)
        END_DATE="$2"
        shift 2
        ;;
    --strategy-path)
        STRATEGY_PATH="$2"
        shift 2
        ;;
    --freqai-model)
        FREQAI_MODEL="$2"
        shift 2
        ;;
    *)
        echo "未知选项: $1"
        echo "使用 --help 查看可用选项"
        exit 1
        ;;
    esac
done

# 显示当前配置
echo "=== 回测配置 ==="
echo "配置文件: ${CONFIG_FILE}"
echo "策略名称: ${STRATEGY}"
echo "开始日期: ${START_DATE}"
echo "结束日期: ${END_DATE}"
echo "策略路径: ${STRATEGY_PATH}"
if [[ -n "$FREQAI_MODEL" ]]; then
    echo "FreqAI模型: ${FREQAI_MODEL}"
fi
echo "================"

# 构建freqtrade命令
if [[ -z "$END_DATE" ]]; then
    END_DATE=$(date +"%Y%m%d")
fi

freqtrade_cmd=(
    freqtrade hyperopt
    --data-dir "${DATA_DIR}"
    --config "${CONFIG_FILE}"
    --timerange="${START_DATE}-${END_DATE}"
    --strategy-path "${STRATEGY_PATH}"
    --strategy "${STRATEGY}"
)

freqtrade_cmd+=(
    "${other_args[@]}"
)

# 如果指定了FreqAI模型，添加到命令中
if [[ -n "$FREQAI_MODEL" ]]; then
    freqtrade_cmd+=(--freqaimodel "${FREQAI_MODEL}")
fi

# 执行超参数优化
"${freqtrade_cmd[@]}"
